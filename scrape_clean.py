import os
import re
import requests
from bs4 import BeautifulSoup
from urllib.parse import urlparse

def remove_empty_tags(soup):
    for tag in soup.find_all():
        remove_empty_tags(tag)
        if not tag.contents or (not tag.get_text(strip=True) and not tag.find()):
            tag.decompose()

def clean_html_from_url(url):
    response = requests.get(url)
    response.raise_for_status()
    soup = BeautifulSoup(response.text, 'html.parser')
    main_div = soup.find('div', id='main-content')
    if not main_div:
        print(f"⚠️ No <div id='main-content'> found on: {url}")
        return None
    for tag in main_div.find_all(True):
        tag.attrs = {}
    remove_empty_tags(main_div)
    return main_div.prettify()

def slugify_url(url):
    path = urlparse(url).path.strip('/')
    path = re.sub(r'[^a-zA-Z0-9/-]', '', path).replace('/', '-')
    return path or "index"

def download_ar_market_content(api_url, output_folder="scraped_pages/ar"):
    os.makedirs(output_folder, exist_ok=True)
    print("📡 Fetching market data from API...")
    response = requests.get(api_url)
    response.raise_for_status()
    markets = response.json()

    # Find the AR market
    ar_market = next((m for m in markets if m.get("marketSetting", {}).get("marketCode") == "AR"), None)
    if not ar_market:
        print("❌ AR market not found in API response.")
        return

    domain = ar_market["marketSetting"]["domain"]
    page_urls = ar_market.get("pageUrls", [])
    print(f"🌎 Domain: {domain}")
    print(f"🔗 {len(page_urls)} URLs found for AR market")

    for i, path in enumerate(page_urls, start=1):
        url = f"https://{domain}{path}"
        print(f"🔍 Processing {i}/{len(page_urls)}: {url}")
        try:
            clean_html = clean_html_from_url(url)
            if clean_html:
                filename = slugify_url(path) + ".html"
                filepath = os.path.join(output_folder, filename)
                with open(filepath, "w", encoding="utf-8") as f:
                    f.write(clean_html)
                print(f"✅ Saved: {filepath}")
        except Exception as e:
            print(f"❌ Failed to process {url}: {e}")

# Use the actual API endpoint
api_url = "https://centralapi.ef.com/central-api/common/market/v2/getpages/english-resources/"
download_ar_market_content(api_url)
