<div id="main-content">
 <div>
  <div>
   <section>
    <h1>
     Condicional tipo cero
    </h1>
    <a>
     Prueba tu comprensiÃ³n
    </a>
    <div>
     <section>
      <article>
       <div>
        <div>
         <div>
          <div>
           <h3>
            FormaciÃ³n
           </h3>
           <p>
            En las oraciones condicionales de tipo 0 ("zero conditional"), el tiempo verbal en ambas proposiciones es el "simple present".
           </p>
           <table>
            <thead>
             <tr>
              <th>
               ProposiciÃ³n "if" (condiciÃ³n)
              </th>
              <th>
               ProposiciÃ³n principal (resultado)
              </th>
             </tr>
            </thead>
            <tbody>
             <tr>
              <td>
               <strong>
                "If" + "simple present"
               </strong>
              </td>
              <td>
               <strong>
                "simple present"
               </strong>
              </td>
             </tr>
             <tr>
              <td>
               If this thing happens
              </td>
              <td>
               that thing happens.
              </td>
             </tr>
            </tbody>
           </table>
           <p>
            Como ocurre con todas las oraciones condicionales, el orden de las proposiciones no es fijo. Es posible que sea necesario modificar los pronombres y la puntuaciÃ³n al revertir el orden de las proposiciones pero el significado de la oraciÃ³n no cambiarÃ¡. En las oraciones de "zero conditional" se puede sustituir "if" por "when" sin afectar al significado, puesto que ambos tÃ©rminos sirven para expresar hechos generales.
           </p>
           <div>
            <h5>
             Ejemplos
            </h5>
            <ul>
             <li>
              If you heat ice, it melts.
             </li>
             <li>
              Ice melts if you heat it.
             </li>
             <li>
              When you heat ice, it melts.
             </li>
             <li>
              Ice melts when you heat it.
             </li>
             <li>
              If it rains, the grass gets wet.
             </li>
             <li>
              The grass gets wet if it rains.
             </li>
             <li>
              When it rains, the grass gets wet.
             </li>
             <li>
              The grass gets wet when it rains.
             </li>
            </ul>
           </div>
           <h3>
            Funciones
           </h3>
           <p>
            El condicional tipo 0 se emplea para realizar afirmaciones sobre el mundo real y suele referirse a hechos generales que damos por ciertos, como los hechos cientÃ­ficos. En estas oraciones el marco temporal es
            <strong>
             ahora o siempre
            </strong>
            y la situaciÃ³n es
            <strong>
             real y posible
            </strong>
            .
           </p>
           <div>
            <h5>
             Ejemplos
            </h5>
            <ul>
             <li>
              If you freeze water, it becomes a solid.
             </li>
             <li>
              Plants die if they don't get enough water.
             </li>
             <li>
              If my husband has a cold, I usually catch it.
             </li>
             <li>
              If public transport is efficient, people stop using their cars.
             </li>
             <li>
              If you mix red and blue, you get purple.
             </li>
            </ul>
           </div>
           <p>
            El condicional tipo cero suele tambiÃ©n utilizarse para dar instrucciones y, en este caso, el verbo de la clÃ¡usula principal va en imperativo.
           </p>
           <div>
            <h5>
             Ejemplos
            </h5>
            <ul>
             <li>
              If Bill phones, tell him to meet me at the cinema.
             </li>
             <li>
              Ask Pete if you're not sure what to do.
             </li>
             <li>
              If you want to come, call me before 5:00.
             </li>
             <li>
              Meet me here if we get separated.
             </li>
            </ul>
           </div>
          </div>
         </div>
        </div>
       </div>
      </article>
     </section>
    </div>
   </section>
  </div>
 </div>
</div>
