<div id="main-content">
 <div>
  <div>
   <section>
    <h1>
     GÃ©nero de los nombres
    </h1>
    <div>
     <section>
      <article>
       <div>
        <div>
         <div>
          <div>
           <p>
            Los nombres responden a las preguntas
            <strong>
             "Â¿QuÃ© es?"
            </strong>
            y
            <strong>
             "Â¿QuiÃ©n es?"
            </strong>
            . Permiten nombrar objetos, personas y lugares.Â
           </p>
           <div>
            <h5>
             Ejemplos
            </h5>
            <ul>
             <li>
              dog
             </li>
             <li>
              bicycle
             </li>
             <li>
              Mary
             </li>
             <li>
              girl
             </li>
             <li>
              beauty
             </li>
             <li>
              France
             </li>
             <li>
              world
             </li>
            </ul>
           </div>
           <p>
            En general, los nombres ingleses no hacen ninguna distinciÃ³n entre masculino, femenino y neutro. No obstante, a veces el gÃ©nero se indica con diferentes formas o palabras cuando hablamos de personas o animales.
           </p>
           <div>
            <h5>
             <PERSON><PERSON><PERSON><PERSON>
            </h5>
            <table>
             <thead>
              <tr>
               <th>
                <PERSON><PERSON><PERSON><PERSON>
               </th>
               <th>
                <PERSON><PERSON><PERSON>
               </th>
               <th>
                Neutro
               </th>
              </tr>
             </thead>
             <tbody>
              <tr>
               <td>
                man
               </td>
               <td>
                woman
               </td>
               <td>
                person
               </td>
              </tr>
              <tr>
               <td>
                father
               </td>
               <td>
                mother
               </td>
               <td>
                parent
               </td>
              </tr>
              <tr>
               <td>
                boy
               </td>
               <td>
                girl
               </td>
               <td>
                child
               </td>
              </tr>
              <tr>
               <td>
                uncle
               </td>
               <td>
                aunt
               </td>
               <td>
                Â
               </td>
              </tr>
              <tr>
               <td>
                husband
               </td>
               <td>
                wife
               </td>
               <td>
                spouse
               </td>
              </tr>
              <tr>
               <td>
                actor
               </td>
               <td>
                actress
               </td>
               <td>
                Â
               </td>
              </tr>
              <tr>
               <td>
                prince
               </td>
               <td>
                princess
               </td>
               <td>
                Â
               </td>
              </tr>
              <tr>
               <td>
                waiter
               </td>
               <td>
                waitress
               </td>
               <td>
                server
               </td>
              </tr>
              <tr>
               <td>
                rooster
               </td>
               <td>
                hen
               </td>
               <td>
                chicken
               </td>
              </tr>
              <tr>
               <td>
                stallion
               </td>
               <td>
                mare
               </td>
               <td>
                horse
               </td>
              </tr>
             </tbody>
            </table>
           </div>
           <p>
            La mayorÃ­a de los nombres que se refieren a roles y profesiones pueden utilizarse tanto con un sujeto masculino como con un sujeto femenino, por ejemplo:
            <em>
             cousin, teenager, teacher, doctor, student, friend, colleague
            </em>
           </p>
           <div>
            <h5>
             Ejemplos
            </h5>
            <ul>
             <li>
              Mary is my friend. She is a doctor.
             </li>
             <li>
              Peter is my cousin. He is a doctor.
             </li>
             <li>
              Arthur is my friend. He is a student.
             </li>
             <li>
              Jane is my cousin. She is a student.
             </li>
            </ul>
           </div>
           <p>
            Es posible hacer una distinciÃ³n de gÃ©nero con estos tÃ©rminos aÃ±adiendo las palabras
            <em>
             male
            </em>
            o
            <em>
             female
            </em>
            .
           </p>
           <div>
            <h5>
             Ejemplos
            </h5>
            <ul>
             <li>
              Sam is a female doctor.
             </li>
             <li>
              No, he is not my boyfriend, he is just a male friend.
             </li>
             <li>
              I have three female cousins and two male cousins.
             </li>
            </ul>
           </div>
           <p>
            Aunque es poco frecuente, podemos atribuir gÃ©nero a cosas que no lo tienen mediante un pronombre masculino o femenino, para mostrar familiaridad con ellas. Es igualmente correcto utilizar el pronombre neutro (
            <em>
             it
            </em>
            ).
           </p>
           <div>
            <h5>
             Ejemplos
            </h5>
            <ul>
             <li>
              I love my car.
              <strong>
               She
              </strong>
              (the car) is my greatest passion.
             </li>
             <li>
              France is popular with
              <strong>
               her
              </strong>
              (France's) neighbours at the moment.
             </li>
             <li>
              I travelled from England to New York on the Queen Elizabeth;
              <strong>
               she
              </strong>
              (the Queen Elizabeth) is a great ship.
             </li>
            </ul>
           </div>
           <p>
            Â
           </p>
          </div>
         </div>
        </div>
       </div>
      </article>
     </section>
    </div>
   </section>
  </div>
 </div>
</div>
