<div id="main-content">
 <div>
  <div>
   <section>
    <h1>
     El punto
    </h1>
    <div>
     <section>
      <article>
       <div>
        <div>
         <div>
          <div>
           <p>
            El punto ("full stop" en inglÃ©s britÃ¡nico, "period" en inglÃ©s americano) probablemente sea el signo de puntuaciÃ³n mÃ¡s fÃ¡cil de usar. Se puede emplear como un cuchillo, para cortar las oraciones a la medida necesaria. En general, es posible dividir las oraciones colocando un punto al final de una idea lÃ³gica y completa que nos suene bien.
           </p>
           <h6>
            Indicar el final de una oraciÃ³n no interrogativa o exclamativa
           </h6>
           <div>
            <h5>
             Ejemplos
            </h5>
            <ul>
             <li>
              Rome is the capital of Italy.
             </li>
             <li>
              I was born in Australia and now live in Indonesia.
             </li>
             <li>
              The <PERSON><PERSON> is the spiritual leader of the Tibetan people.
             </li>
            </ul>
           </div>
           <h6>
            Indicar una abreviatura
           </h6>
           <p>
            Numerosas abreviaturas requieren el uso de puntos. "Dr", "Mr", "Mrs" y "Ms" no llevan punto en inglÃ©s britÃ¡nico, al igual que ocurre con la mayorÃ­a de abreviaturas compuestas por mayÃºsculas iniciales, como "MA", "Phd" o "CIA". En inglÃ©s americano, algunas de estas abreviaturas sÃ­ requieren el uso del punto o bien se consideran correctas ambas formas (con y sin punto). Si necesitas que tu puntuaciÃ³n tenga una precisiÃ³n del 100%, consulta una guÃ­a de estilo detallada para ver las normas de uso de las abreviaturas en la variedad de inglÃ©s que estÃ©s utilizando.
           </p>
           <div>
            <h5>
             Ejemplos
            </h5>
            <ul>
             <li>
              I will arrive between 6 a.m. and 7 a.m.
             </li>
             <li>
              We are coming on Fri., Jan. 4.
             </li>
            </ul>
           </div>
           <h6>
            Puntos suspensivos
           </h6>
           <p>
            Con frecuencia, se ven oraciones terminadas con tres puntos seguidos. En inglÃ©s, esto indica que solo se ha citado una parte de la oraciÃ³n o del texto, o bien que se deja en manos del lector el completar la idea.
           </p>
           <div>
            <h5>
             Ejemplos
            </h5>
            <ul>
             <li>
              The Lord's Prayer begins, "Our Father which art in Heaven..."
             </li>
             <li>
              He is always late, but you know how I feel about that...
             </li>
            </ul>
           </div>
           <h6>
            Punto tras una sola palabra
           </h6>
           <p>
            En ocasiones, una Ãºnica palabra puede constituir una oraciÃ³n. En este caso, se pone punto detrÃ¡s de ella igual que se harÃ­a con cualquier otra oraciÃ³n. Esto suele darse cuando el sujeto se sobreentiende, como ocurre con un saludo o una orden.
           </p>
           <div>
            <h5>
             Ejemplos
            </h5>
            <ul>
             <li>
              "Goodbye."
             </li>
             <li>
              "Stop."
             </li>
            </ul>
           </div>
           <h6>
            Los puntos y las cifras
           </h6>
           <p>
            En inglÃ©s, se utiliza el punto para separar los nÃºmeros enteros y decimales. Cuando este signo se usa con cifras, tambiÃ©n se denomina "decimal point" y se lee como "point" salvo que haga referencia a dinero.
           </p>
           <div>
            <h5>
             Ejemplos
            </h5>
            <ul>
             <li>
              $10.43 = ten dollars and 43 cents
             </li>
             <li>
              14.17 = fourteen point one seven
             </li>
            </ul>
           </div>
          </div>
         </div>
        </div>
       </div>
      </article>
     </section>
    </div>
   </section>
  </div>
 </div>
</div>
