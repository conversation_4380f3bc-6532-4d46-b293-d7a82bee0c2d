import requests
from bs4 import BeautifulSoup
from urllib.parse import urlparse

TARGET_CLASSES = {
    "field",
    "field-item",
    "field-items",
    "field-label-hidden",
    "field-name-body",
    "field-type-text-with-summary"
}

def find_target_classes_in_main_content(url):
    try:
        response = requests.get(url)
        response.raise_for_status()
        soup = BeautifulSoup(response.text, 'html.parser')

        main_div = soup.find('div', id='main-content')
        if not main_div:
            print(f"⚠️ No <div id='main-content'> on: {url}")
            return set()

        found_classes = set()
        for tag in main_div.find_all(class_=True):
            tag_classes = tag.get("class", [])
            for cls in tag_classes:
                if cls.strip() in TARGET_CLASSES:
                    found_classes.add(cls.strip())

        return found_classes
    except Exception as e:
        print(f"❌ Error on {url}: {e}")
        return set()

def scan_ar_market_for_target_classes(api_url):
    print("📡 Fetching AR market data...")
    response = requests.get(api_url)
    response.raise_for_status()
    markets = response.json()

    ar_market = next((m for m in markets if m.get("marketSetting", {}).get("marketCode") == "AR"), None)
    if not ar_market:
        print("❌ AR market not found.")
        return

    domain = ar_market["marketSetting"]["domain"]
    page_urls = ar_market.get("pageUrls", [])

    class_usage = {cls: [] for cls in TARGET_CLASSES}

    for i, path in enumerate(page_urls, start=1):
        url = f"https://{domain}{path}"
        print(f"🔍 [{i}/{len(page_urls)}] Scanning: {url}")
        used = find_target_classes_in_main_content(url)

        for cls in used:
            class_usage[cls].append(url)

    print("\n📊 Class usage report:\n")
    for cls in sorted(TARGET_CLASSES):
        pages = class_usage[cls]
        print(f"▶️ {cls} ({len(pages)} page(s))")
        for page in pages:
            print(f"   - {page}")
        print()

# ---- Run it ----
api_url = "https://centralapi.ef.com/central-api/common/market/v2/getpages/english-resources/"
scan_ar_market_for_target_classes(api_url)
