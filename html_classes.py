import requests
from bs4 import BeautifulSoup
from urllib.parse import urlparse

def extract_classes_in_main_content(url):
    try:
        response = requests.get(url)
        response.raise_for_status()
        soup = BeautifulSoup(response.text, 'html.parser')

        main_div = soup.find('div', id='main-content')
        if not main_div:
            print(f"⚠️ No <div id='main-content'> on: {url}")
            return set()

        class_names = set()
        for tag in main_div.find_all(class_=True):
            classes = tag.get("class", [])
            for cls in classes:
                class_names.add(cls.strip())

        return class_names
    except Exception as e:
        print(f"❌ Error on {url}: {e}")
        return set()

def list_ar_main_content_classes(api_url):
    print("📡 Getting AR market URLs...")
    response = requests.get(api_url)
    response.raise_for_status()
    markets = response.json()

    ar_market = next((m for m in markets if m.get("marketSetting", {}).get("marketCode") == "AR"), None)
    if not ar_market:
        print("❌ AR market not found.")
        return

    domain = ar_market["marketSetting"]["domain"]
    page_urls = ar_market.get("pageUrls", [])

    all_classes = set()

    for i, path in enumerate(page_urls, start=1):
        url = f"https://{domain}{path}"
        print(f"🔍 [{i}/{len(page_urls)}] Scanning: {url}")
        page_classes = extract_classes_in_main_content(url)
        all_classes.update(page_classes)

    print("\n✅ Unique classes inside #main-content:")
    for cls in sorted(all_classes):
        print(f"- {cls}")

# ---- Run it ----
api_url = "https://centralapi.ef.com/central-api/common/market/v2/getpages/english-resources/"
list_ar_main_content_classes(api_url)
